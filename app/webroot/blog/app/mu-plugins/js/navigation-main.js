// Configuration
const BV_NAV = {
    debug: false,
    prefix: {
        general: '[Navigation]',
        mmenu: '[MMenu]',
        megamenu: '[Megamenu]'
    },
    endpoints: {
        mobileMenu: '/mmenu',
        megaMenu: '/megamenu'
    },
    megaMenuLoading: false,
    megaMenuLoaded: false,
    megaMenuInitialized: false,
    mmenuLoading: false,
    mmenuInitialized: false
};

// Logger utility
const logger = {
    log: function(message, data, type = 'general') {
        if (!BV_NAV.debug) return;
        const prefix = BV_NAV.prefix[type] || BV_NAV.prefix.general;
        console.log(prefix, message, data || '');
    },
    error: function(message, error, type = 'general') {
        const prefix = BV_NAV.prefix[type] || BV_NAV.prefix.general;
        console.error(prefix + ' ERROR:', message, error || '');
    },
    important: function(message, data, type = 'general') {
        const prefix = BV_NAV.prefix[type] || BV_NAV.prefix.general;
        console.log(prefix, message, data || '');
    },
    endpoint: function(message, data, type = 'general') {
        // Always log endpoint messages regardless of debug setting
        const prefix = BV_NAV.prefix[type] || BV_NAV.prefix.general;
        console.log(prefix + ' ENDPOINT:', message, data || '');
    }
};

logger.important('Navigation.js loading...', null, 'general');

// Function to load menu content from endpoints
function loadMenuContent(endpoint, targetSelector) {
    return new Promise((resolve, reject) => {
        // Determine the appropriate prefix based on the endpoint
        const type = endpoint.includes('mmenu') ? 'mmenu' : 'megamenu';
        const prefix = BV_NAV.prefix[type] || BV_NAV.prefix.general;

        // Always log endpoint requests, regardless of debug setting
        console.log(prefix + ' ENDPOINT: Loading content from ' + endpoint + ' into ' + targetSelector);

        fetch(endpoint)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.text();
            })
            .then(html => {
                console.log(prefix + ' ENDPOINT: Content loaded successfully from ' + endpoint);

                // Check if flash message exists before navigation loading
                const flashDebugBefore = document.querySelector('[style*="FLASH DEBUG"]');
                if (flashDebugBefore) {
                    console.log(prefix + ' FLASH IMPACT: Flash debug element found before navigation load');
                } else {
                    console.log(prefix + ' FLASH IMPACT: No flash debug element found before navigation load');
                }

                const target = document.querySelector(targetSelector);
                if (target) {
                    // Log the first 100 characters of the HTML for debugging
                    console.log(prefix + ' ENDPOINT: Content preview: ' + html.substring(0, 100).replace(/\n/g, ' ') + '...');
                    target.innerHTML = html;

                    // Log success with more details
                    console.log(prefix + ' ENDPOINT: Content inserted into ' + targetSelector + ' (' + target.tagName + ')');

                    // Check if flash message exists after navigation loading
                    const flashDebugAfter = document.querySelector('[style*="FLASH DEBUG"]');
                    if (flashDebugAfter) {
                        console.log(prefix + ' FLASH IMPACT: Flash debug element still exists after navigation load');
                    } else {
                        console.log(prefix + ' FLASH IMPACT: Flash debug element disappeared after navigation load');
                    }

                    resolve(html);
                } else {
                    const error = new Error('Target element not found: ' + targetSelector);
                    console.error(prefix + ' ENDPOINT ERROR: ' + error.message);
                    reject(error);
                }
            })
            .catch(error => {
                console.error(prefix + ' ENDPOINT ERROR: Failed to load content from ' + endpoint, error);
                reject(error);
            });
    });
}

// Track initialization to prevent duplication
let navigationInitialized = false;

document.addEventListener("DOMContentLoaded", function() {
    if (navigationInitialized) {
        logger.important('Navigation already initialized, skipping duplicate initialization', null, 'general');
        return;
    }
    navigationInitialized = true;

    logger.important('Initializing navigation-main.js...', null, 'general');

    // Add a direct event listener to the menu trigger that doesn't depend on mmenu initialization
    const menuTrigger = document.querySelector('.nav-toggle.mmenu-trigger');
    if (menuTrigger) {
        logger.important('Adding direct click handler to menu trigger', null, 'mmenu');
        menuTrigger.addEventListener('click', function(e) {
            e.preventDefault();

            // If mmenu is initialized and we have an API, use it
            if (window.mmenuAPI) {
                const isMenuOpen = document.body.classList.contains('mm-wrapper--opened');
                if (!isMenuOpen) {
                    // Show cross icon
                    this.classList.add('active');
                    const hamburgerIcon = this.querySelector('.hamburger-icon');
                    const crossIcon = this.querySelector('.cross-icon');
                    if (hamburgerIcon) hamburgerIcon.style.display = 'none';
                    if (crossIcon) crossIcon.style.display = 'block';
                    window.mmenuAPI.open();
                } else {
                    window.mmenuAPI.close();
                }
            } else {
                // Fallback behavior if mmenu isn't initialized
                logger.important('Mmenu API not available, using fallback behavior', null, 'mmenu');
                this.classList.toggle('active');
                const hamburgerIcon = this.querySelector('.hamburger-icon');
                const crossIcon = this.querySelector('.cross-icon');

                if (this.classList.contains('active')) {
                    // Show cross icon
                    if (hamburgerIcon) hamburgerIcon.style.display = 'none';
                    if (crossIcon) crossIcon.style.display = 'block';

                    // Try to find and show the mobile menu
                    const mobileMenu = document.querySelector('#mobile-menu');
                    if (mobileMenu) {
                        mobileMenu.classList.add('mm-menu--opened');
                        document.body.classList.add('mm-wrapper--opened');
                    }
                } else {
                    // Show hamburger icon
                    if (hamburgerIcon) hamburgerIcon.style.display = 'block';
                    if (crossIcon) crossIcon.style.display = 'none';

                    // Try to hide the mobile menu
                    const mobileMenu = document.querySelector('#mobile-menu');
                    if (mobileMenu) {
                        mobileMenu.classList.remove('mm-menu--opened');
                        document.body.classList.remove('mm-wrapper--opened');
                    }
                }
            }
        });
        logger.important('Direct click handler added to menu trigger', null, 'mmenu');
    } else {
        logger.error('Menu trigger element not found (.nav-toggle.mmenu-trigger)', null, 'mmenu');
    }

    // Add initializeMmenu function to BV_NAV
    BV_NAV.initializeMmenu = initializeMmenu;

    // Make BV_NAV globally available
    window.BV_NAV = BV_NAV;

    // Log configuration for debugging
    logger.important('BV_NAV configuration:', BV_NAV, 'general');

    // Load menu content from endpoints
    // Check for mobile menu element
    const mobileMenu = document.querySelector('#mobile-menu');
    if (mobileMenu) {
        logger.important('Mobile menu found', null, 'mmenu');

        // Log details about the mobile menu element
        logger.important('Mobile menu details:', null, 'mmenu');
        logger.important(`- Parent: ${mobileMenu.parentNode ? mobileMenu.parentNode.tagName : 'none'}`, null, 'mmenu');
        logger.important(`- Parent class: ${mobileMenu.parentNode ? mobileMenu.parentNode.className : 'none'}`, null, 'mmenu');
        logger.important(`- Classes: ${mobileMenu.className}`, null, 'mmenu');
        logger.important(`- Children: ${mobileMenu.children.length}`, null, 'mmenu');
        logger.important(`- First child: ${mobileMenu.children.length > 0 ? mobileMenu.children[0].tagName : 'none'}`, null, 'mmenu');

        // Check if the mobile menu is inside the page-header__inner element
        const isInPageHeader = mobileMenu.closest('.page-header__inner') !== null;
        logger.important('Mobile menu is inside page-header__inner: ' + isInPageHeader, null, 'mmenu');

        // No need to reposition the mobile menu as it's now directly in the template
    } else {
        logger.important('No mobile menu found, will wait for it to be added', null, 'mmenu');
    }

    if (document.querySelector('#mobile-menu')) {
        logger.important('Loading mobile menu content from ' + BV_NAV.endpoints.mobileMenu, null, 'mmenu');

        // Set flag to indicate we're loading the mmenu
        BV_NAV.mmenuLoading = true;

        // Make BV_NAV globally available for navigation.js to check
        window.BV_NAV = BV_NAV;

        // Log the initial state of the mobile menu
        const initialMobileMenu = document.querySelector('#mobile-menu');
        logger.important('Initial mobile menu HTML structure:', null, 'mmenu');
        logger.important('Has children: ' + (initialMobileMenu.children.length > 0), null, 'mmenu');
        if (initialMobileMenu.children.length > 0) {
            logger.important('First child tag: ' + initialMobileMenu.children[0].tagName, null, 'mmenu');
            if (initialMobileMenu.children[0].tagName === 'UL') {
                const firstUl = initialMobileMenu.children[0];
                logger.important('Number of list items: ' + firstUl.children.length, null, 'mmenu');
                if (firstUl.children.length > 0) {
                    const firstLi = firstUl.children[0];
                    logger.important('First list item classes: ' + firstLi.className, null, 'mmenu');
                }
            }
        }

        // We'll initialize the mmenu after the content is loaded
        loadMenuContent(BV_NAV.endpoints.mobileMenu, '#mobile-menu')
            .then(() => {
                logger.important('Mobile menu content loaded successfully', null, 'mmenu');

                // Reset loading flag
                BV_NAV.mmenuLoading = false;

                // Update global BV_NAV
                window.BV_NAV = BV_NAV;

                // Log the updated state of the mobile menu
                const updatedMobileMenu = document.querySelector('#mobile-menu');
                logger.important('Updated mobile menu HTML structure:', null, 'mmenu');
                logger.important('Has children: ' + (updatedMobileMenu.children.length > 0), null, 'mmenu');
                if (updatedMobileMenu.children.length > 0) {
                    logger.important('First child tag: ' + updatedMobileMenu.children[0].tagName, null, 'mmenu');
                    if (updatedMobileMenu.children[0].tagName === 'UL') {
                        const firstUl = updatedMobileMenu.children[0];
                        logger.important('Number of list items: ' + firstUl.children.length, null, 'mmenu');
                        if (firstUl.children.length > 0) {
                            const firstLi = firstUl.children[0];
                            logger.important('First list item classes: ' + firstLi.className, null, 'mmenu');
                        }
                    }
                }

                // Now initialize the mmenu with the loaded content
                initializeMmenu();
            })
            .catch(error => {
                logger.error('Failed to load mobile menu', error, 'mmenu');

                // Reset loading flag on error
                BV_NAV.mmenuLoading = false;

                // Update global BV_NAV
                window.BV_NAV = BV_NAV;

                // Try to initialize anyway with whatever content is available
                initializeMmenu();
            });
    }

    // Find the first megamenu container - prefer the static one
    const megaMenuContainers = document.querySelectorAll('.mega-menu');
    const megaMenuContainer = megaMenuContainers.length > 0 ? megaMenuContainers[0] : null;

    if (megaMenuContainer) {
        // If there are multiple megamenu elements, log a warning
        if (megaMenuContainers.length > 1) {
            logger.important('Multiple mega-menu elements found (' + megaMenuContainers.length + '). Using the first one.', null, 'megamenu');
        }

        logger.important('Loading mega menu content from ' + BV_NAV.endpoints.megaMenu + ' into existing mega-menu', null, 'megamenu');

        // Set flag to indicate we're loading the megamenu
        BV_NAV.megaMenuLoading = true;

        // Make BV_NAV globally available for navigation.js to check
        window.BV_NAV = BV_NAV;

        // Ensure the container has an ID for easier targeting
        if (!megaMenuContainer.id) {
            megaMenuContainer.id = 'megamenu-placeholder';
        }

        loadMenuContent(BV_NAV.endpoints.megaMenu, '#' + megaMenuContainer.id)
            .then(() => {
                logger.important('Mega menu content loaded successfully', null, 'megamenu');

                // Set flag to indicate megamenu is loaded
                BV_NAV.megaMenuLoading = false;
                BV_NAV.megaMenuLoaded = true;

                // Update global BV_NAV
                window.BV_NAV = BV_NAV;

                // Re-initialize MegaMenu now that content is loaded
                logger.important('Re-initializing MegaMenu with loaded content', null, 'megamenu');
                setTimeout(() => {
                    new MegaMenu();
                }, 100); // Small delay to ensure DOM is updated
            })
            .catch(error => {
                logger.error('Failed to load mega menu', error, 'megamenu');

                // Reset flags on error
                BV_NAV.megaMenuLoading = false;
                BV_NAV.megaMenuLoaded = false;

                // Update global BV_NAV
                window.BV_NAV = BV_NAV;
            });
    } else {
        logger.error('No megamenu container found. Looking for #megamenu-placeholder or .mega-menu', null, 'megamenu');
    }

    // We'll initialize the mmenu after the content is loaded from the endpoint
    // See the initializeMmenu function below

    // Add media query listener
    const mediaQuery = window.matchMedia('(min-width: 1024px)');
    logger.log('Media query initialized');

    function handleViewportChange(e) {
        logger.log('Viewport change detected', { matches: e.matches });

        if (e.matches) {
            // Desktop view - close mobile menu if open
            logger.log('Switching to desktop view');
            const mmenuElement = document.querySelector('#mobile-menu');

            if (mmenuElement) {
                // Try different methods to get the API
                const mmenuAPI = mmenuElement.mmApi ||
                                window.mmenu?.getInstance(mmenuElement) ||
                                mmenuElement.M_mmenu;

                if (mmenuAPI) {
                    try {
                        mmenuAPI.close();
                        logger.log('Mobile menu closed for desktop view');

                        // Reset hamburger/cross icons
                        const trigger = document.querySelector('.mmenu-trigger');
                        if (trigger) {
                            trigger.classList.remove('active');
                            const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                            const crossIcon = trigger.querySelector('.cross-icon');
                            if (hamburgerIcon && crossIcon) {
                                hamburgerIcon.style.display = 'block';
                                crossIcon.style.display = 'none';
                            }
                        }
                    } catch (error) {
                        logger.error('Error closing mmenu:', error);

                        // Fallback: try to remove opened class
                        mmenuElement.classList.remove('mm-menu--opened');
                        document.body.classList.remove('mm-wrapper--opened');

                        // Reset hamburger/cross icons
                        const trigger = document.querySelector('.mmenu-trigger');
                        if (trigger) {
                            trigger.classList.remove('active');
                            const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                            const crossIcon = trigger.querySelector('.cross-icon');
                            if (hamburgerIcon && crossIcon) {
                                hamburgerIcon.style.display = 'block';
                                crossIcon.style.display = 'none';
                            }
                        }

                        logger.log('Used class removal fallback');
                    }
                } else {
                    // If we can't find the API, try the class removal fallback
                    mmenuElement.classList.remove('mm-menu--opened');
                    document.body.classList.remove('mm-wrapper--opened');

                    // Reset hamburger/cross icons
                    const trigger = document.querySelector('.mmenu-trigger');
                    if (trigger) {
                        trigger.classList.remove('active');
                        const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                        const crossIcon = trigger.querySelector('.cross-icon');
                        if (hamburgerIcon && crossIcon) {
                            hamburgerIcon.style.display = 'block';
                            crossIcon.style.display = 'none';
                        }
                    }

                    logger.log('No API found - used class removal fallback');
                }
            }
        } else {
            // Mobile view
            logger.log('Switching to mobile view');
        }
    }

    // Add the listener
    try {
        // Modern browsers
        if (typeof mediaQuery.addEventListener === 'function') {
            mediaQuery.addEventListener('change', handleViewportChange);
        } else if (typeof mediaQuery.addListener === 'function') {
            // Fallback for older browsers (deprecated but kept for compatibility)
            mediaQuery.addListener(handleViewportChange);
        } else {
            logger.error('No supported media query listener method available');
        }
    } catch (err) {
        logger.error('Could not add media query listener:', err);
    }

    // Check initial state
    handleViewportChange(mediaQuery);

    // Function to initialize the mmenu
    function initializeMmenu() {
        try {
            // Check if the mobile menu exists and has content
            const mobileMenu = document.querySelector("#mobile-menu");
            if (!mobileMenu) {
                logger.error('Mobile menu element not found', null, 'mmenu');
                return;
            }

            // Check if mmenu is already initialized to prevent duplicate initialization
            if (mobileMenu.classList.contains('mm-menu')) {
                logger.important('Mmenu already initialized, skipping initialization', null, 'mmenu');
                return;
            }

            // Log the mobile menu structure before initialization
            logger.important('Mobile menu structure before initialization:', null, 'mmenu');
            logger.important('Has children: ' + (mobileMenu.children.length > 0), null, 'mmenu');

            // Check if the mobile menu is empty (which is expected since we're using an empty fallback)
            if (mobileMenu.children.length === 0 || (mobileMenu.children.length === 1 && mobileMenu.textContent.trim() === '')) {
                logger.important('Mobile menu is empty, which is expected. Content will be loaded from the main site.', null, 'mmenu');
            } else if (mobileMenu.children.length > 0) {
                logger.important('First child tag: ' + mobileMenu.children[0].tagName, null, 'mmenu');
                if (mobileMenu.children[0].tagName === 'UL') {
                    const firstUl = mobileMenu.children[0];
                    logger.important('Number of list items: ' + firstUl.children.length, null, 'mmenu');
                    if (firstUl.children.length > 0) {
                        const firstLi = firstUl.children[0];
                        logger.important('First list item classes: ' + firstLi.className, null, 'mmenu');
                    }
                }
            }

            logger.important('Initializing mmenu', null, 'mmenu');

            // Verify the mobile menu is in the correct position (inside page-header__inner)
            const menuElement = document.querySelector("#mobile-menu");
            if (!menuElement) {
                logger.error('Mobile menu element not found before initialization', null, 'mmenu');
                return;
            }

            // Check if the mobile menu is inside the page-header__inner element
            const isInPageHeader = menuElement.closest('.page-header__inner') !== null;
            logger.important('Mobile menu is inside page-header__inner: ' + isInPageHeader, null, 'mmenu');

            // No need to reposition the mobile menu as it's now directly in the template

            // Re-enabled: Mmenu initialization
            logger.important('Initializing mmenu in navigation-main.js', null, 'mmenu');

            // Use the appropriate constructor (Mmenu or mmenu)
            const MmenuConstructor = window.Mmenu || window.mmenu;
            const menu = new MmenuConstructor("#mobile-menu", {
                "offCanvas": {
                    "position": "left",
                    "onClick": {
                        "close": false
                    }
                },
                "scrollBugFix": { "fix": true },
                "theme": "light",
                "slidingSubmenus": true,
                "extensions": [
                    "pagedim-black",
                ],
                "navbar": {
                    "title": "Menu",
                    "titleLink": "parent"
                },
                "navbars": [
                    {
                        content: [
                            '<a href="/" style="display: block; text-align: center;"><img src="/img/site/sprites/logos/bv-logo-red.svg" alt="Bon Voyage" style="height: 50px;"></a>',

                        ],
                        position: "top"
                    },
                    {
                        "position": "top",
                        "content": [
                            "prev",
                            "title",
                        ]
                    }
                ],
                "hooks": {
                    "openPanel:before": function($panel) {
                        // Remove aria-hidden from all panels when opening
                        document.querySelectorAll('.mm-panel').forEach(panel => {
                            panel.removeAttribute('aria-hidden');
                        });
                    },
                    "closePanel:after": function($panel) {
                        // Use inert attribute instead of aria-hidden when closing
                        document.querySelectorAll('.mm-panel:not(.mm-panel--opened)').forEach(panel => {
                            panel.setAttribute('inert', '');
                        });
                        document.querySelector('.mm-panel--opened')?.removeAttribute('inert');
                    },
                    "close:after": function() {
                        // Reset hamburger/cross icons when menu is closed
                        const trigger = document.querySelector('.mmenu-trigger');
                        if (trigger) {
                            trigger.classList.remove('active');
                            const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                            const crossIcon = trigger.querySelector('.cross-icon');
                            if (hamburgerIcon && crossIcon) {
                                hamburgerIcon.style.display = 'block';
                                crossIcon.style.display = 'none';
                            }
                        }
                    }
                }
            });

            const api = menu.API;

            api.bind("open:after", function() {
              logger.important('Locking scroll for mobile menu', null, 'mmenu');
              document.documentElement.style.overflow = "hidden";
            });

            api.bind("close:after", function() {
              logger.important('Unlocking scroll after mobile menu close', null, 'mmenu');
              document.documentElement.style.overflow = "";
            });

            // We've already added a direct event listener at the top level
            // that works regardless of mmenu initialization status.
            // Just store the API globally so that listener can use it.
            logger.important('Mmenu API available, direct click handler will use it', null, 'mmenu');

            // Store the API globally for debugging and access from other scripts
            window.mmenuAPI = api;

            // Set a global flag to indicate that mmenu has been initialized
            // This will be checked by navigation.js to prevent duplicate initialization
            window.BV_NAV = window.BV_NAV || {};
            window.BV_NAV.mmenuInitialized = true;

            logger.important('Mmenu initialized successfully', null, 'mmenu');
            logger.log('Mmenu details', {
                menu: menu,
                api: api
            });

            // Add additional debugging to verify API is working
            logger.important('Setting up megamenu dropdown handlers', null, 'megamenu');
            const dropdownTriggers = document.querySelectorAll('[data-dropdown]');
            logger.important('Number of dropdown triggers: ' + dropdownTriggers.length, null, 'megamenu');

        } catch (error) {
            logger.error('Mmenu initialization error:', error);
        }
    }
});
// Remove the global error handler for now

// Track MegaMenu instance
let megaMenuInstance = null;

class MegaMenu {
    constructor() {
        // Allow re-initialization if content has been loaded
        if (megaMenuInstance && !BV_NAV.megaMenuLoaded) {
            logger.log('MegaMenu instance already exists, returning existing instance');
            return megaMenuInstance;
        }

        // If we're re-initializing after content load, reset the instance
        if (megaMenuInstance && BV_NAV.megaMenuLoaded) {
            logger.important('Re-initializing MegaMenu after content load', null, 'megamenu');
            megaMenuInstance = null;
        }

        logger.important('MegaMenu: Initializing...', null, 'megamenu');
        this.triggers = document.querySelectorAll('[data-dropdown]');

        // Find the first megamenu container - prefer the static one
        const megaMenuContainers = document.querySelectorAll('.mega-menu');
        const megaMenuContainer = megaMenuContainers.length > 0 ? megaMenuContainers[0] : null;

        // If there are multiple megamenu elements, log a warning
        if (megaMenuContainers.length > 1) {
            logger.important('MegaMenu: Multiple mega-menu elements found (' + megaMenuContainers.length + '). Using the first one.', null, 'megamenu');
        }

        this.dropdowns = megaMenuContainer ? megaMenuContainer.querySelectorAll('.mega-menu__panel') : document.querySelectorAll('.mega-menu__panel');

        this.sublistTriggers = document.querySelectorAll('.mega-menu__item:has(.mega-menu__sublist)');
        logger.important('MegaMenu: Found ' + this.triggers.length + ' triggers, ' +
                         this.dropdowns.length + ' dropdowns, ' +
                         this.sublistTriggers.length + ' sublist triggers', null, 'megamenu');
        this.activeDropdown = null;
        this.activeSublist = null;
        this.hoverDelay = 200;
        this.hoverTimeout = null;
        this.init();
        this.setupSublistHandlers();

        // Store instance
        megaMenuInstance = this;

        // Set global flag to indicate megamenu is initialized
        BV_NAV.megaMenuInitialized = true;
        window.BV_NAV = BV_NAV;

        logger.important('MegaMenu: Set global initialization flag', null, 'megamenu');
    }

    setupSublistHandlers() {
        this.sublistTriggers.forEach(item => {
            const link = item.querySelector('.mega-menu__link');
            const sublist = item.querySelector('.mega-menu__sublist');

            if (link && sublist) {
                // Mouse enter on trigger
                item.addEventListener('mouseenter', () => {
                    this.openSublist(sublist, link);
                });

                // Mouse leave on trigger
                item.addEventListener('mouseleave', (e) => {
                    if (!e.relatedTarget || !e.relatedTarget.closest('.mega-menu__sublist')) {
                        this.closeSublist(sublist, link);
                    }
                });

                // Mouse enter on sublist
                sublist.addEventListener('mouseenter', () => {
                    clearTimeout(this.hoverTimeout);
                    link.style.setProperty('background-color', '#f5f5f5', 'important');
                    link.style.setProperty('color', '#a80000', 'important');
                });

                // Mouse leave on sublist
                sublist.addEventListener('mouseleave', () => {
                    this.closeSublist(sublist, link);
                });
            }
        });
    }

    openSublist(sublist, link) {
        const item = link.closest('.mega-menu__item');
        const dropdown = item.closest('.mega-menu__panel');
        const list = item.closest('ul');

        // Get the li's position relative to its parent ul
        const itemRect = item.getBoundingClientRect();
        const listRect = list.getBoundingClientRect();
        const relativeTop = itemRect.top - listRect.top;

        // Calculate top position using actual heights
        const sublistHeight = sublist.offsetHeight;
        const linkHeight = link.offsetHeight;
        const maxOffset = sublistHeight - linkHeight;
        const listHeight = list.offsetHeight;
        const percentage = relativeTop / listHeight;
        const topOffset = -maxOffset * percentage;

        // Position the sublist
        sublist.style.top = `${topOffset}px`;

        // Ensure consistent horizontal positioning
        sublist.style.left = '100%';
        sublist.style.position = 'absolute';
        sublist.style.transform = 'none';
        sublist.style.bottom = 'auto';

        // Set visibility and styling
        sublist.style.setProperty('opacity', '1', 'important');
        sublist.style.setProperty('visibility', 'visible', 'important');
        link.style.setProperty('background-color', '#f5f5f5', 'important');
        link.style.setProperty('color', '#a80000', 'important');
        this.activeSublist = sublist;
    }

    closeSublist(sublist, link) {
        sublist.style.setProperty('opacity', '0', 'important');
        sublist.style.setProperty('visibility', 'hidden', 'important');
        link.style.removeProperty('background-color');
        link.style.removeProperty('color');
        if (this.activeSublist === sublist) {
            this.activeSublist = null;
        }
    }

    init() {
        this.triggers.forEach(trigger => {
            const dropdownId = trigger.getAttribute('data-dropdown');
            const dropdown = document.getElementById(dropdownId);
            const parentListItem = trigger.closest('.secondary-nav li');
            logger.log('MegaMenu: Setting up trigger for dropdown:', dropdownId);

            if (dropdown && parentListItem) {
                logger.log('MegaMenu: Found matching dropdown for:', dropdownId);

                // Mouse enter on trigger
                trigger.addEventListener('mouseenter', () => {
                    logger.log('MegaMenu: Mouse enter on trigger:', dropdownId);
                    clearTimeout(this.hoverTimeout);

                    // Close all other dropdowns before opening this one
                    this.dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            const otherTriggerId = otherDropdown.id;
                            const otherTrigger = document.querySelector(`[data-dropdown="${otherTriggerId}"]`);
                            const otherListItem = otherTrigger?.closest('.secondary-nav li');
                            this.closeDropdown(otherDropdown, otherTrigger, otherListItem);
                        }
                    });

                    // Open the current dropdown
                    this.openDropdown(dropdown, trigger, parentListItem);
                });

                // Mouse leave on trigger
                trigger.addEventListener('mouseleave', (e) => {
                    // Check if mouse moved to the dropdown
                    if (!e.relatedTarget || !e.relatedTarget.closest('.mega-menu__panel')) {
                        this.startCloseTimer(dropdown, trigger, parentListItem);
                    }
                });

                // Mouse enter on dropdown
                dropdown.addEventListener('mouseenter', () => {
                    logger.log('MegaMenu: Mouse enter on dropdown:', dropdownId);
                    clearTimeout(this.hoverTimeout);
                    parentListItem.classList.add('is-active');
                });

                // Mouse leave on dropdown
                dropdown.addEventListener('mouseleave', (e) => {
                    // Check if mouse moved to the trigger
                    if (!e.relatedTarget || !e.relatedTarget.closest('[data-dropdown]')) {
                        this.startCloseTimer(dropdown, trigger, parentListItem);
                    }
                });
            }
        });

        logger.important('MegaMenu: Initialization complete', null, 'megamenu');
    }

    startCloseTimer(dropdown, trigger, parentListItem) {
        this.hoverTimeout = setTimeout(() => {
            this.closeDropdown(dropdown, trigger, parentListItem);
        }, this.hoverDelay);
    }

    openDropdown(dropdown, trigger, parentListItem) {
        dropdown.style.setProperty('display', 'block', 'important');
        parentListItem.classList.add('is-active');
        this.activeDropdown = dropdown;
    }

    closeDropdown(dropdown, trigger, parentListItem) {
        dropdown.style.setProperty('display', 'none', 'important');
        parentListItem.classList.remove('is-active');
        if (this.activeDropdown === dropdown) {
            this.activeDropdown = null;
        }
    }

    closeAllDropdowns() {
        logger.log('MegaMenu: Closing all dropdowns');
        this.dropdowns.forEach(dropdown => {
            const triggerId = dropdown.id;
            const trigger = document.querySelector(`[data-dropdown="${triggerId}"]`);
            const parentListItem = trigger?.closest('.secondary-nav li');
            this.closeDropdown(dropdown, trigger, parentListItem);
        });
    }
}

// Track initialization to prevent duplication
let megaMenuInitialized = false;

// Initialize when DOM is ready and all scripts are loaded
window.addEventListener('load', () => {
    if (!megaMenuInitialized) {
        logger.important('Window loaded, initializing MegaMenu');
        megaMenuInitialized = true;
        new MegaMenu(); // No need to store the instance as it's tracked internally
    }
});
