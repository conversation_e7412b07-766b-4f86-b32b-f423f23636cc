<?php
  echo $this->element('modules/page_content_header', array(
    'header'   => 'Request a Quote'
  ));
?>

<section class="page-content-body js-page-content-body">
  <div class="page-content-body__inner">
    <div class="page-content-body__content">
      <div class="content-blocks content-blocks--contact-us">
        <?php if ($session->check('Message.flash.params.hide_form')): ?>
          <?php
            $javascript->codeBlock(
              "Event.observe(window, 'load', function(e){try{window.ga('send','pageview','/quote-request-submitted');}catch(e){}});",
              array(
                'inline' => false
              )
            );
            $javascript->codeBlock(
              "Event.observe(window, 'load', function(e){try{window.fbq('track','Lead');}catch(e){}});",
              array(
                'inline' => false
              )
            );
          ?>

          <div class="content-block">
            <h2>Thank you!</h2>

            <h3>We will be in touch.</h3>
          </div>

          <?php if($session->check('Message.flash')) { $session->flash(); } ?>

          <script type="text/javascript">
            /* <![CDATA[ */
            var google_conversion_id = 1030719956;
            var google_conversion_language = "en";
            var google_conversion_format = "1";
            var google_conversion_color = "ffffff";
            var google_conversion_label = "_MOACOzopAUQ1JO-6wM";
            var google_conversion_value = 0;
          </script>

          <script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js"></script>

          <noscript>
            <div style="display:inline;">
              <img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/1030719956/?value=0&amp;label=_MOACOzopAUQ1JO-6wM&amp;guid=ON&amp;script=0"/>
            </div>
          </noscript>
        <?php else: ?>
          <div class="content-block">
            <h2>Your holiday starts right here</h2>

            <p>
              The first step is for us to learn a little about you and your ideas to enable our team of experts to start to build a picture of your ideal trip.
            </p>

            <p>
              At Bon Voyage we don’t just generate &quot;automated&quot; quotes; rather we individually tailor each holiday so please do provide a telephone number and an email address.
            </p>

            <p class="content-block__link">
              <?php
                echo $html->link('Would you like to us call you instead?', array(
                  'controller' => 'contacts',
                  'action'     => 'contact_form'
                ));
              ?>
            </p>
          </div>

          <div class="content-block">
            <h3>First a bit about you</h3>

            <?php
            // Debug flash message
            echo '<div style="background: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin: 10px 0;">';
            echo '[FLASH DEBUG] Checking for flash message...<br>';
            if ($session->check('Message.flash')) {
                echo '[FLASH DEBUG] Flash message exists!<br>';
                echo '[FLASH DEBUG] Flash data: ' . print_r($session->read('Message.flash'), true) . '<br>';
            } else {
                echo '[FLASH DEBUG] No flash message found<br>';
            }
            echo '</div>';

            $session->flash();
            ?>

            <p>All fields marked with a '*' are mandatory.</p>

            <?php
              echo $webAdmin->formCreate('QuoteRequest', array(
                  'url' => array('action' => 'add', '#' => 'flashMessage'),
                  'class' => 'contact-form'
              ));

              $titles = array('Mr','Mrs','Ms','Miss','Mstr','Dr');

              echo $form->input('title', array('label' => 'Title*:', 'options' => array_combine($titles, $titles), 'empty' => 'Please select'));

              echo $form->input('firstname', array('label' => 'First name:*', 'type' => 'text'));

              echo $form->input('lastname', array('label' => 'Last name*:', 'type' => 'text'));

              echo $form->input('telephone', array('label' => 'Daytime telephone number*:', 'type' => 'text'));

              echo $form->input('email', array('label' => 'Email address:*', 'type' => 'text'));

              echo $form->input('house_num_name', array('label' => 'House Name/No:', 'type' => 'text'));

              echo $form->input('postcode', array('label' => 'Postcode:', 'type' => 'text'));

            ?>

            <h3>How many are travelling?</h3>


            <?php
              echo $form->input('num_adults', array('label' => 'No. of adults*:', 'type' => 'text'));

              echo $form->input('num_children', array('label' => 'No. of children:', 'type' => 'text'));
            ?>

            <h3>Your holiday</h3>

            <?php
              echo $form->input('preferred_month', array('label' => 'Preferred date of travel*:', 'type' => 'select', 'empty' => 'Please select month'));

              echo $form->input('preferred_year', array('label' => '', 'type' => 'text', 'empty' => 'Please select year'));

              echo $form->input('preferred_duration', array('label' => 'Preferred holiday duration*:', 'type' => 'text'));

              echo $form->input('airport', array('label' => 'Preferred UK departure airport*:', 'type' => 'text'));

              echo $form->input('destinations', array('label' => 'Where do you wish to visit?*', 'type' => 'textarea'));

              echo $form->input('suggest', array('label' => 'I am open to suggestions and ideas', 'type' => 'checkbox'));
              ?>
              <div class="preferred-date">
                  <?php echo $form->input('preferred_date', array('label' => 'When do you want to visit?:', 'type' => 'text')); ?>
              </div>
              <?php

              $accomStarsError = $form->error('accom_stars');
            ?>

            <div class="input radio radioQuoteRequestAccomStars<?php echo !is_null($accomStarsError) ? ' error' : ''; ?>">
                <?php
                  echo $form->label('accom_stars', 'What accommodation grade would you like?*');

                  $starOptions = array('4 Star' => '4 Star - High Standards and amenities', '5 Star' => '5 Star - A touch of luxury', 'Combination' => 'Combination');

                  echo $form->radio('accom_stars', $starOptions, array('legend' => false, 'class' => 'testing'));

                  if ($accomStarsError) { echo $accomStarsError; }
                ?>
            </div>

            <?php
              echo $form->input('other_info', array('label' => 'Please let us know any other information that you think would assist us in providing you with a personal quote:'));

              echo $form->input('how_heard', array('label' => 'How did you hear about this site?', 'type' => 'select', 'empty' => 'Please select'));

              echo $form->input('email_optin', array('label' => 'Send me the best in ideas and inspiration for my USA or Canada holiday. We won’t overload your inbox and every email will contain a simple one-click unsubscribe link.', 'type' => 'checkbox'));

              echo '<button>Request a Quote</button>';

              echo $form->end();
            ?>
          </div>
        <?php endif ?>
      </div>
    </div>
    <?php echo $this->element('sidebar'); ?>
  </div>
</section>
